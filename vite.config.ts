import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tsconfigPaths from 'vite-tsconfig-paths';

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      'components': '/src/components',
      'theme': '/src/theme',
      'layouts': '/src/layouts',
      'vbrae-utils': '/src/vbrae-utils',
      'assets': '/src/assets',
      'routes': '/src/routes',
      'hooks': '/src/hooks',
      'data': '/src/data',
      'functions': '/src/functions',
    },
  },
  plugins: [
    tsconfigPaths(),
    react(),
    // checker({
    //   typescript: true,
    //   eslint: {
    //     lintCommand: 'eslint "./src/**/*.{ts,tsx}"',
    //   },
    // }),
  ],
  optimizeDeps: {
    include: ['@tinymce/tinymce-react']
  },
  preview: {
    port: 5000,
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
  },
});

export interface ConversationIdDto {
    _id: string;
    offer: null;
    client: string;
    seller: string;
    unreadClient: number;
    unreadSeller: number;
    archived: boolean;
    type: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
}

export interface TicketDto {
    _id: string;
    name: string;
    email: string;
    subject: string;
    inquiryType: string;
    description: string;
    attachments: string[];
    status: string;
    createdAt: string;
    ticketNumber: string;
    updatedAt: string;
    __v: number;

    conversationId: ConversationIdDto | string | null;
}
import useTicketDetails from 'pages/tickets/hooks/useTicketDetails.ts';
import { useParams, useSearchParams } from 'react-router-dom';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import FileViewer from 'pages/tickets/components/FileViewer.tsx';
import SupportChat from 'pages/tickets/components/SupportChat.tsx';
import Alert from '@mui/material/Alert';
import { useEffect } from 'react';

export default function TicketDetails() {
  const { id = '' } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const { details, detailsLoading } = useTicketDetails({ id });

  // Extract conversation ID from ticket details or URL params
  const getConversationId = () => {
    const urlConversationId = searchParams.get('conversationId');
    if (urlConversationId) return urlConversationId;

    if (details?.conversationId) {
      if (typeof details.conversationId === 'object' && details.conversationId._id) {
        return details.conversationId._id;
      }
      if (typeof details.conversationId === 'string') {
        return details.conversationId;
      }
    }
    return null;
  };

  // Update URL with conversation ID when ticket details load
  useEffect(() => {
    if (details && !searchParams.get('conversationId')) {
      const conversationId = getConversationId();
      if (conversationId) {
        setSearchParams({ conversationId });
      }
    }
  }, [details, searchParams, setSearchParams]);

  if (!details || detailsLoading) return <CircularProgress />;

  const conversationId = getConversationId();
  const hasConversation = !!conversationId;

  return (
    <Stack spacing={2} direction="column">
      <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
          <Typography variant="h5" fontWeight={600}>
            {details.ticketNumber}
          </Typography>
        </Stack>
      </Paper>

      <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
        <Stack
          direction={{ xs: 'column' }}
          gap={{
            xs: 0,
            sm: 2,
          }}
        >
          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Name:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.name}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Email:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.email}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Subject:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.subject}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Type:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.inquiryType}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Description:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.description}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Status:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.status}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Created At
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.createdAt.split('T')[0]}
            </Typography>
          </Box>
        </Stack>
      </Paper>

      {/* Support Chat Section */}
      <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
        <Typography variant="h5" fontWeight={600} sx={{ mb: 2 }}>
          Support Chat
        </Typography>
        {hasConversation ? (
          <SupportChat />
        ) : (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              No conversation record found for this ticket. The chat feature is not available.
            </Typography>
          </Alert>
        )}
      </Paper>

      <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
        <Typography variant="h5" fontWeight={600}>
          Attachments
        </Typography>
        <Box>
          {details.attachments.length > 0 ? (
            details.attachments.map((fileUrl, index) => (
              <FileViewer key={index} fileUrl={fileUrl} />
            ))
          ) : (
            <Typography variant="body2" color="text.secondary">
              No attachments found for this ticket.
            </Typography>
          )}
        </Box>
      </Paper>
    </Stack>
  );
}